using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddAgencyPermissionsStructure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("44d4e474-9e62-4f0f-96b0-5aee2975021f"));

            migrationBuilder.CreateTable(
                name: "AgencyDefaultPermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    DisplayName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgencyDefaultPermissions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AgencyPermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AgencyId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    AgencyDefaultPermissionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DateCreated = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateModified = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LanguageId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgencyPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgencyPermissions_AgencyDefaultPermissions_AgencyDefaultPermissionId",
                        column: x => x.AgencyDefaultPermissionId,
                        principalTable: "AgencyDefaultPermissions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "6e6809a3-9fd4-43ff-bf3a-71cf82882d5b", "AQAAAAIAAYagAAAAELMab0b1NvqZPoR6QAy+kN4Oh2tCZNkoq+Lii7pWMuh0oZOQmgKuDdmC8OYgJdkdqg==", "bf025158-f83c-43c8-9099-2f90f73b5869" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "532a3085-9b5e-4989-afe8-6ae4f556e627", "AQAAAAIAAYagAAAAEO1Qhfz39V/EPlaJcokEs0wOMStolKMoGYWGMDFBvO0Iv4UYQPOeTqZLTfo75hIMTw==", "37897088-d73a-489d-98f4-7ee1db565132" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("f330a0f1-fc51-479c-89bd-7d168c38cbc6"), 13, new DateTime(2025, 6, 10, 8, 43, 47, 84, DateTimeKind.Local).AddTicks(7594), new DateTime(2025, 6, 10, 8, 43, 47, 84, DateTimeKind.Local).AddTicks(7619), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });

            migrationBuilder.CreateIndex(
                name: "IX_AgencyPermissions_AgencyDefaultPermissionId",
                table: "AgencyPermissions",
                column: "AgencyDefaultPermissionId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgencyPermissions");

            migrationBuilder.DropTable(
                name: "AgencyDefaultPermissions");

            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("f330a0f1-fc51-479c-89bd-7d168c38cbc6"));

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "222fe146-4a07-4205-9325-9e775a185f4c", "AQAAAAIAAYagAAAAEI+daznZ8HtWd7swV6CcbjdRGQgbPNzIZUFZcTknbiaYqeJLUQ/CC/opsjzD/egdpQ==", "2f459191-58bf-48a0-82dd-6a3c0162befa" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "ce498cf9-bdea-4563-afc7-b270b36230d2", "AQAAAAIAAYagAAAAEHglKzMYwFJXtetH8KUDLz56UJ/UQBKvEluXdBGFjI1tuXSJW1CzHEnU6Upj2Uxwrw==", "6bfdc861-9eda-442e-8697-fb14c9bd049e" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("44d4e474-9e62-4f0f-96b0-5aee2975021f"), 13, new DateTime(2025, 5, 24, 20, 34, 51, 208, DateTimeKind.Local).AddTicks(1406), new DateTime(2025, 5, 24, 20, 34, 51, 208, DateTimeKind.Local).AddTicks(1424), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
