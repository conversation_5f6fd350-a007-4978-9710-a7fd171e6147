using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddPriority : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("b5515224-ba5c-44d3-a7c0-bc8c02786951"));

            migrationBuilder.AddColumn<int>(
                name: "Priority",
                table: "SpecialOfferWidgets",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Destination",
                table: "RoundtripOfferWidgets",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "Origin",
                table: "RoundtripOfferWidgets",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "Priority",
                table: "RoundtripOfferWidgets",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "Origin",
                table: "ExploreOfferWidgets",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "Priority",
                table: "ExploreOfferWidgets",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Priority",
                table: "DiscoverOfferWidgets",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Priority",
                table: "CustomizableOfferWidgets",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "Tag",
                table: "CustomizableOfferWidgets",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "ad4cdde0-3f2a-4a54-87be-747d470777c4", "AQAAAAIAAYagAAAAEB6ynWRbqj1UFH6BWUaWUOg7/gsijGKVi/SBTsaqrqkbtJmiEXWeZyMiyzriF+dE5g==", "989ce18f-75e1-421e-8413-9a25e15c42d8" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "c128689f-b03b-44d4-a1ca-ffec427d5e45", "AQAAAAIAAYagAAAAEEyKjFMBPHPGdrshzYZ3cNLSHBX1RZG70Sz5OmlR+0lC8busqr44FOgHm3KD44hRPw==", "1f1a8196-1fad-4fc0-bcc8-1c0d03734342" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("33294ccf-e8d7-42b3-bc36-3ad9b566e5d8"), 13, new DateTime(2025, 4, 21, 9, 44, 5, 905, DateTimeKind.Local).AddTicks(1516), new DateTime(2025, 4, 21, 9, 44, 5, 905, DateTimeKind.Local).AddTicks(1558), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("33294ccf-e8d7-42b3-bc36-3ad9b566e5d8"));

            migrationBuilder.DropColumn(
                name: "Priority",
                table: "SpecialOfferWidgets");

            migrationBuilder.DropColumn(
                name: "Destination",
                table: "RoundtripOfferWidgets");

            migrationBuilder.DropColumn(
                name: "Origin",
                table: "RoundtripOfferWidgets");

            migrationBuilder.DropColumn(
                name: "Priority",
                table: "RoundtripOfferWidgets");

            migrationBuilder.DropColumn(
                name: "Origin",
                table: "ExploreOfferWidgets");

            migrationBuilder.DropColumn(
                name: "Priority",
                table: "ExploreOfferWidgets");

            migrationBuilder.DropColumn(
                name: "Priority",
                table: "DiscoverOfferWidgets");

            migrationBuilder.DropColumn(
                name: "Priority",
                table: "CustomizableOfferWidgets");

            migrationBuilder.DropColumn(
                name: "Tag",
                table: "CustomizableOfferWidgets");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "d88068d5-271f-4edf-8a12-8d0ce1751c37", "AQAAAAIAAYagAAAAEN1c2OgLMtIt0ShXzAYN63IJgJG4ZYAKU+hGbDeMvsceZaaADsMYwxQhLHbi4puVGg==", "8752713c-3fc5-4473-b337-0ab6baa6a0f3" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "fcb1118c-7daa-4149-8cb0-31e6af050791", "AQAAAAIAAYagAAAAEA3qY/j0hxW5L2zNe1f8RK+gFdn4siHdYfGWDSJUFeO0taIy/DhQaBJXc4eKPj2QTw==", "f37a9113-e8b6-47c4-bd77-3e0d4baf7566" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("b5515224-ba5c-44d3-a7c0-bc8c02786951"), 13, new DateTime(2025, 4, 9, 14, 48, 37, 537, DateTimeKind.Local).AddTicks(9180), new DateTime(2025, 4, 9, 14, 48, 37, 537, DateTimeKind.Local).AddTicks(9220), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
