using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace TmobCore.Cms.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddStatusToAirport : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("d8d1362c-2629-41ea-bedd-0688a60e79f1"));

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "Airports",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "b2242b6c-3630-4a07-9c6c-e80695c30199", "AQAAAAIAAYagAAAAEIFMOCBaVND/tPquIagF+Hax1Qrfpj/UNXOFKyTDjDVEiAbAPAaSRWKZ3fhvaXybDQ==", "2be463c4-1114-411d-9721-6a55bc5d55df" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "77950971-612f-4b89-b172-908092e2f44c", "AQAAAAIAAYagAAAAEBbgz32yNX4843BztcTmsXIyjsNzaE6yD+YmRc+YI7rVs3KdPO1cBH0Okgb6IvbsBw==", "0ce1a92e-11e4-4dd0-9f3c-02e0ffdac2ca" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("b7973710-d254-4190-898d-1efb5701711e"), 13, new DateTime(2025, 7, 7, 12, 6, 47, 108, DateTimeKind.Local).AddTicks(1100), new DateTime(2025, 7, 7, 12, 6, 47, 108, DateTimeKind.Local).AddTicks(1150), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Example",
                keyColumn: "Id",
                keyValue: new Guid("b7973710-d254-4190-898d-1efb5701711e"));

            migrationBuilder.DropColumn(
                name: "Status",
                table: "Airports");

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "302dffb7-ba4e-44e2-b77a-1b258954f9d5",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "198081c8-9dce-4919-ae49-1eb3b6dc2554", "AQAAAAIAAYagAAAAEJ68TMP1IV8iwKuIxRZUS6itUlTkWFiuVk28Ep7XQf3LC0niGjLc3TXkCW8aQaf2dg==", "c9370fe2-1033-45d0-b0a7-8d0af70f3975" });

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "b28b07da-03a3-435a-a370-2a675c11a57d",
                columns: new[] { "ConcurrencyStamp", "PasswordHash", "SecurityStamp" },
                values: new object[] { "3e93ffd1-bda7-4cb5-aa3f-4a430b764bd1", "AQAAAAIAAYagAAAAEE/L/5BQEiBlxsuvH5IEi5srSsXIiymkRqKVgJO4M31OdCgtxUDFwZEVjUqdVTzA3w==", "508eebd9-d6a0-44cc-93fa-7d65205f9ac6" });

            migrationBuilder.InsertData(
                table: "Example",
                columns: new[] { "Id", "Count", "DateCreated", "DateModified", "Description", "LanguageId", "Name" },
                values: new object[] { new Guid("d8d1362c-2629-41ea-bedd-0688a60e79f1"), 13, new DateTime(2025, 7, 5, 17, 23, 1, 466, DateTimeKind.Local).AddTicks(7720), new DateTime(2025, 7, 5, 17, 23, 1, 466, DateTimeKind.Local).AddTicks(7750), "The modelBuilder.Entity<Example>().HasData() method in Entity Framework is used to seed initial data into the database for a specific entity (in this case, the \"Example\" entity).", null, "First Data" });
        }
    }
}
