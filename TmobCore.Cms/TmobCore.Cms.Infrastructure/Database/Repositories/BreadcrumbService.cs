using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Infrastructure.Database.DatabaseContext;

namespace TmobCore.Cms.Infrastructure.Database.Repositories
{
    public class BreadcrumbService : IBreadcrumbService
    {
        private readonly CmsCoreDatabaseContext _context;

        public BreadcrumbService(CmsCoreDatabaseContext context)
        {
            _context = context;
        }

        public async Task<List<string>> GetPathAsync(Guid categoryId)
        {
            var path = new List<string>();

            while (true)
            {
                var category = await _context.HelpCategories
                    .Where(x => x.Id == categoryId)
                    .Select(x => new { x.Title, x.ParentCategoryId })
                    .FirstOrDefaultAsync();

                if (category == null)
                    break;

                path.Insert(0, category.Title);

                if (category.ParentCategoryId == null)
                    break;

                categoryId = category.ParentCategoryId.Value;
            }

            return path;
        }
    }
}