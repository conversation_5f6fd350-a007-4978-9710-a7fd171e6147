using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Common;
using TmobCore.Cms.Domain.Entities;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Infrastructure.Database.Configurations;

public class ImageConfiguration : IEntityTypeConfiguration<Image>
{
    public void Configure(EntityTypeBuilder<Image> builder)
    {
        builder.HasKey(i => i.Id);
        builder.Property(i => i.Description).IsRequired();
        builder.Property(i => i.ImageCode).IsRequired();
        builder.Property(i => i.Status).HasDefaultValue(BaseStatus.Draft);
        builder.Property(i => i.Deleted).HasDefaultValue(false);
        builder.Property(i => i.ImageType).HasDefaultValue(ImageType.None);
        builder.Property(i => i.FileId);
        builder.Property(i => i.Extension);
        builder.Property(i => i.AltText);
    }
}