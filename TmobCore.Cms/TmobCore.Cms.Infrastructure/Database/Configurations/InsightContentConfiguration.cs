using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Infrastructure.Database.Configurations
{
    public class InsightContentConfiguration : IEntityTypeConfiguration<InsightContent>
    {
        public void Configure(EntityTypeBuilder<InsightContent> builder)
        {
            builder.HasKey(ic => ic.Id);
            builder.Property(ic => ic.Content).IsRequired();
            builder.Property(ic => ic.Order).HasDefaultValue(0);
            builder.Property(ic => ic.Deleted).HasDefaultValue(false);

            builder.HasOne(ic => ic.Insight)
                .WithMany(i => i.Contents)
                .HasForeignKey(ic => ic.InsightId);

            builder.HasOne(ic => ic.Image)
                .WithMany()
                .HasForeignKey(ic => ic.FileId)
                .OnDelete(DeleteBehavior.SetNull);
        }
    }
}