using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Infrastructure.Database.Configurations
{
    public class AirportConfiguration : IEntityTypeConfiguration<Airport>
    {
        public void Configure(EntityTypeBuilder<Airport> builder)
        {
            builder.HasKey(a => a.Id);
            builder.Property(a => a.Code).IsRequired().HasMaxLength(10);
            builder.Property(a => a.Name).IsRequired().HasMaxLength(255);
            builder.Property(a => a.Latitude).IsRequired();
            builder.Property(a => a.Longitude).IsRequired();
            builder.Property(a => a.AlternativeAirports).HasMaxLength(1000);
            builder.Property(a => a.IsActive).HasDefaultValue(true);
            builder.Property(a => a.IsDeleted).HasDefaultValue(false);
            builder.Property(a => a.Status).HasDefaultValue(Domain.Common.BaseStatus.Draft);
            
            builder.HasOne(a => a.City)
                   .WithMany()
                   .HasForeignKey(a => a.CityId);
            
            builder.HasOne(a => a.Image)
                .WithMany()
                .HasForeignKey(a => a.ImageId)
                .OnDelete(DeleteBehavior.SetNull);
        }
    }
}
