using TmobCore.Cms.Domain.Common;

namespace TmobCore.Cms.Domain.Entities
{
    public class Airport : BaseEntity
    {
        public Guid UserId { get; set; }
        public Guid ProjectId { get; set; }
        public Guid GroupId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string AlternativeAirports { get; set; }
        public int Order { get; set; }        
        public Guid CityId { get; set; }
        public City City { get; set; }
        public bool IsActive { get; set; }
        public bool IsDeleted { get; set; }
        public BaseStatus Status { get; set; } = BaseStatus.Draft;

        public string Slug { get; set; } = String.Empty;
        
        public Guid? ImageId { get; set; }
        
        public virtual Image Image { get; set; }
        
    }
}
