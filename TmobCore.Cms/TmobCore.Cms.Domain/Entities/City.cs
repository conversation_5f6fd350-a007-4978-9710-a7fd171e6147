using TmobCore.Cms.Domain.Common;

namespace TmobCore.Cms.Domain.Entities
{
    public class City : BaseEntity
    {
        public Guid UserId { get; set; }
        public Guid ProjectId { get; set; }
        public Guid GroupId { get; set; }
        public string Name { get; set; }
        public string? Description { get; set; }
        public int Order { get; set; }       
        public Guid? ImageId { get; set; }
        public Guid CountryId { get; set; }
        public Country? Country { get; set; }
        public bool IsDeleted { get; set; } = false;
        public BaseStatus Status { get; set; } = BaseStatus.Draft;
        public string Slug { get; set; } = String.Empty;
        public virtual Image Image { get; set; }
    }
}
