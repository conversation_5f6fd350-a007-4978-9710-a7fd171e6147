using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.FileManagement;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.Contracts.FileManagement
{
    public interface IFileManagementService
    {
        Task<ActionResponse<UploadFileResponse>> UploadFileAsync(UploadFileRequest uploadFileRequest);
        ActionResponse<string> GetFileUrl(Guid fileId, Guid projectId, string extention);
        ActionResponse<string> GetFileUrl(Guid fileId, Guid projectId, string path, string extention);
        Task CreateFolder(Folder folder);
        Task RenameFolderAsync(Folder folder, string newName);
        Task DeleteFolderAsync(Folder folder);
    }
}
