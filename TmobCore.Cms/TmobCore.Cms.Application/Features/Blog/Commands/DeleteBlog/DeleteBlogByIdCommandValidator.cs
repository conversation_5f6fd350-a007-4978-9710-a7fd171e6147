using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;
namespace TmobCore.Cms.Application.Features.Blog.Commands.DeleteBlog
{
    public class DeleteBlogByIdCommandValidator : AbstractValidator<DeleteBlogByIdCommand>
    {
        private readonly IBlogRepository _blogRepository;

        public DeleteBlogByIdCommandValidator(IBlogRepository blogRepository)
        {
            _blogRepository = blogRepository;

            RuleFor(p => p.BlogGroupIds)
                .NotEmpty().WithMessage("Blog IDs cannot be empty")
                .Must(x => x != null && x.Any()).WithMessage("At least one Blog ID must be provided");
        }
    }
}