using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;

namespace TmobCore.Cms.Application.Features.Blog.Queries.CheckBlogSlug;

public class CheckBlogSlugQueryValidator : AbstractValidator<CheckBlogSlugQuery>
{
    private readonly IBlogRepository _blogRepository;

    public CheckBlogSlugQueryValidator(IBlogRepository blogRepository)
    {
        _blogRepository = blogRepository;

        RuleFor(x => x.Slug)
            .NotEmpty()
            .NotNull()
            .WithMessage("Slug is required");
    }
}
