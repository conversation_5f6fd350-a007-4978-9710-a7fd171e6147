using MediatR;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Campaigns.Commands.CreateCampaign;

public class CreateCampaignCommand : IRequest<ActionResponse<bool>>
{
    public List<CreateCampaignRequest> Campaigns { get; set; }  
}

public class CreateCampaignRequest : BaseRequest
{
    public string Title { get; set; }
    public string Slug { get; set; }
    public Guid CampaignTypeId { get; set; }
    public int Order { get; set; }
    public DateTime StartDate { get; set; } = DateTime.MinValue;
    public DateTime EndDate { get; set; } = DateTime.MinValue;
    public DateTime PublishDate { get; set; } = DateTime.MinValue;
    public string Content { get; set; }
    public string CampaignTerms { get; set; } = string.Empty;
    public BaseStatus Status { get; set; }
    public Guid? ImageId { get; set; }
    public string? CampaignFile { get; set; }
    public string CampaignFileExtention { get; set; } = string.Empty;
    public int ExcelColumnIndex { get; set; }
}