using FluentValidation;
using TmobCore.Cms.Application.Contracts.Persistence;

namespace TmobCore.Cms.Application.Features.Destination.Commands.CreateDestination
{
    public class CreateDestinationValidator : AbstractValidator<CreateDestinationCommand>
    {
        private readonly IDestinationRepository _destinationRepository;

        public CreateDestinationValidator(IDestinationRepository destinationRepository)
        {
            _destinationRepository = destinationRepository;

            RuleForEach(x => x.Destinations).ChildRules(Destinations =>
            {
                Destinations.RuleFor(p => p.Title)
                .NotEmpty().WithMessage("{PropertyName} is required.")
                .NotNull().WithMessage("{PropertyName} is required.")
                .Must(x => !string.IsNullOrWhiteSpace(x)).WithMessage("{PropertyName} cannot be empty or whitespace.")
                .MaximumLength(200).WithMessage("{PropertyName} must not exceed 200 characters.");

                Destinations.RuleFor(p => p.Slug)
                    .NotEmpty().WithMessage("{PropertyName} is required.")
                    .NotNull().WithMessage("{PropertyName} is required.")
                    .MaximumLength(200).WithMessage("{PropertyName} must not exceed 200 characters.")
                    .MustAsync(async (slug, token) =>
                    {
                        return await _destinationRepository.IsSlugUnique(slug);
                    }).WithMessage("A destination with the same slug already exists.");

                Destinations.RuleFor(p => p.BeginDate)
                    .NotEmpty().WithMessage("{PropertyName} is required.")
                    .NotNull().WithMessage("{PropertyName} is required.");

                Destinations.RuleFor(p => p.EndDate)
                    .NotEmpty().WithMessage("{PropertyName} is required.")
                    .NotNull().WithMessage("{PropertyName} is required.")
                    .GreaterThan(p => p.BeginDate).WithMessage("{PropertyName} must be greater than BeginDate");

                Destinations.RuleFor(p => p.Order)
                    .NotNull().WithMessage("{PropertyName} is required.")
                    .GreaterThan(0).WithMessage("{PropertyName} must be greater than 0");

                Destinations.RuleFor(p => p.Contents)
                    .NotNull().WithMessage("Contents cannot be null")
                    .Must(contents => contents != null && contents.Any())
                    .WithMessage("At least one content item is required.");

                Destinations.RuleForEach(p => p.Contents)
                    .ChildRules(content =>
                    {
                        content.RuleFor(x => x.Content)
                            .NotEmpty().WithMessage("Content text is required.")
                            .NotNull().WithMessage("Content text is required.");

                        content.RuleFor(x => x.Order)
                            .NotNull().WithMessage("{PropertyName} is required.")
                            .GreaterThan(0).WithMessage("{PropertyName} must be greater than 0");
                    });
            });
        }
    }
}
