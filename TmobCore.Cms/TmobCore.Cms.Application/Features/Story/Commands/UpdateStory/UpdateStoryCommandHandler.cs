using MediatR;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Story.Commands.UpdateStory;

public class UpdateStoryCommandHandler : IRequestHandler<UpdateStoryCommand, ActionResponse<Guid>>
{
    private readonly IAppLogger<UpdateStoryCommandHandler> _logger;
    private readonly IStoryRepository _storyRepository;
    private readonly IUnitOfWork _uow;
    public UpdateStoryCommandHandler(IStoryRepository storyRepository,
                                    IAppLogger<UpdateStoryCommandHandler> logger, IUnitOfWork uow
                                    )
    {
        _storyRepository = storyRepository;
        _logger = logger;
        _uow = uow;
    }

    public async Task<ActionResponse<Guid>> Handle(UpdateStoryCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var story = await _storyRepository.GetByIdAsync(request.Id);
            if (story != null)
            {
                var validator = new UpdateStoryCommandValidator(_storyRepository);
                var validationResult = await validator.ValidateAsync(request);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning($"Story update failed due to validation errors - {request}");
                    return ActionResponse<Guid>.Fail($"Story update failed due to validation errors - {request}",
                        StatusCode.BadRequest);
                }

                story.CategoryId = request.CategoryId;
                story.Order = request.Order;
                story.StartDate = request.StartDate;
                story.EndDate = request.EndDate;
                story.PublishDate = request.PublishDate;
                story.Tags = request.Tags;
                story.CtaButtonLink = request.CtaButtonLink;
                story.Status = (Domain.Common.BaseStatus)request.Status;
                story.ImageId = FileExtentions.HandleFileIdAssignment(request.ImageId, story.ImageId);

                _storyRepository.Update(story);
                await _uow.SaveChangesAsync(cancellationToken);
                return ActionResponse<Guid>.Success(story.Id, StatusCode.Ok);
            }

            return ActionResponse<Guid>.Fail("Story not found", StatusCode.NotFound);
        }
        catch (Exception ex)
        {
            _logger.LogWarning($"Story update failed due to exception errors Request: {request} - Response: {ex}");
            return ActionResponse<Guid>.Fail(ex.ToString(), StatusCode.BadRequest);
        }
    }
}