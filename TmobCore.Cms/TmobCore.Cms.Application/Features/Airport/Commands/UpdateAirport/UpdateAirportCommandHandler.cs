using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.FileManagement;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Exceptions;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Common;

namespace TmobCore.Cms.Application.Features.Airport.Commands.UpdateAirport
{
    public class UpdateAirportCommandHandler : IRequestHandler<UpdateAirportCommand, ActionResponse<bool>>
    {
        private readonly IAirportRepository _airportRepository;
        private readonly ICityRepository _cityRepository;
        private readonly IUserPrincipal _userPrincipal;
        private readonly IMapper _mapper;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAppLogger<UpdateAirportCommandHandler> _logger;
        public UpdateAirportCommandHandler(IAirportRepository airportRepository,
            ICityRepository cityRepository,
            IUserPrincipal userPrincipal,
            IMapper mapper,
            IUnitOfWork unitOfWork,
            IAppLogger<UpdateAirportCommandHandler> logger)
        {
            _airportRepository = airportRepository;
            _cityRepository = cityRepository;
            _userPrincipal = userPrincipal;
            _mapper = mapper;
            _unitOfWork = unitOfWork;
            _logger = logger;
        }
        public async Task<ActionResponse<bool>> Handle(UpdateAirportCommand request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid()
                ?? throw new NotFoundException("ProjectId", _userPrincipal.ProjectId);

                foreach (var airportRequest in request.UpdateAirportRequests)
                {
                    var airport = await _airportRepository.GetQuery(x => x.Id == airportRequest.Id).FirstOrDefaultAsync(cancellationToken);

                    if (airport == null)
                    {
                        throw new NotFoundException("Airport", airportRequest.Id);
                    }

                    Domain.Entities.City city = null;
                    var mappedAirport = _mapper.Map(airportRequest, airport);
                    mappedAirport.ImageId = FileExtentions.HandleFileIdAssignment(airportRequest.ImageId,airport.ImageId);
                    city = await _cityRepository.GetByIdAsync(airportRequest.CityId);

                    if (city != null)
                    {
                        mappedAirport.CityId = city.Id;
                        //mappedAirport.City = city;
                    }

                    _airportRepository.Update(mappedAirport); 
                }

                await _unitOfWork.SaveChangesAsync(cancellationToken);
                _logger.LogInformation($"Airports is updated successfully.");
                return ActionResponse<bool>.Success(true, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Airport update failed due to an error - {ex.Message}");
                return ActionResponse<bool>.Fail($"Airport update failed due to an error - {ex.Message}", StatusCode.InternalServerError);
            }
        }
    }
}
