using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Country;

namespace TmobCore.Cms.Application.Features.Country.Queries.GetCountryById
{
    public class GetCountryByIdQueryHandler : IRequestHandler<GetCountryByIdQuery, ActionResponse<CountryResponse>>
    {
        private readonly IMapper _mapper;
        private readonly ICountryRepository _countryRepository;
        private readonly IAppLogger<GetCountryByIdQueryHandler> _logger;
        public GetCountryByIdQueryHandler(ICountryRepository countryRepository, IMapper mapper, IAppLogger<GetCountryByIdQueryHandler> logger)
        {
            _countryRepository = countryRepository;
            _mapper = mapper;
            _logger = logger;
        }
        public async Task<ActionResponse<CountryResponse>> Handle(GetCountryByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var country = await _countryRepository.GetQuery(x => x.Id == request.Id && !x.IsDeleted).Include(x => x.Cities).Include(x=>x.Image).FirstOrDefaultAsync(cancellationToken);

                if (country == null)
                {
                    _logger.LogWarning($"Country not found.");
                    return ActionResponse<CountryResponse>.Fail("Country not found.", StatusCode.NotFound);
                }

                var mappedCountry = _mapper.Map<CountryResponse>(country);

                return ActionResponse<CountryResponse>.Success(mappedCountry, StatusCode.Ok);
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Country not found.", request, ex.Message);
                return ActionResponse<CountryResponse>.Fail("Country not found.", StatusCode.InternalServerError);
            }
        }
    }
}
