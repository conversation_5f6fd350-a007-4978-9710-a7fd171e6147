using MediatR;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Enums;

namespace TmobCore.Cms.Application.Features.Image.Commands.UpdateImage;

public class UpdateImageCommand : BaseRequest, IRequest<ActionResponse<Guid>>
{
    public Guid Id { get; set; }
    public string Description { get; set; } = string.Empty;
    public string ImageCode { get; set; } = string.Empty;
    
    public string? FileId { get; set; }
    public string Extention { get; set; } = string.Empty;
    public string? AltText { get; set; }
    public DateTime PublishDate { get; set; } = DateTime.MinValue;
    public ImageType ImageType { get; set; } = ImageType.None;
}