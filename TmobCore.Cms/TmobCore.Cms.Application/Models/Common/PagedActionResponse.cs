using Microsoft.EntityFrameworkCore;

namespace TmobCore.Cms.Application.Models.Common
{
    public class PagedActionResponse<T> : ActionResponse<T>
    {
        public PagedActionResponse(List<T> items, int page, int pageSize)
        {
            Items = items;
            Page = page;
            PageSize = pageSize;
        }
        public List<T> Items { get; }
        public int Page { get; }
        public int PageSize { get; }
        public int TotalCount { get; }
        public bool HasNextPage => PageSize < TotalCount;
        public bool HasPreviousPage => Page > 1;

        public static async Task<ActionResponse<T>> CreateAsync(IQueryable<T> query, int page, int pageSize)
        {
            var totalCount = await query.CountAsync();
            var items = await query.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync();
            return new ActionResponse<T> { Data = default, Items = items, Page = page, PageSize = pageSize, TotalCount = totalCount, IsSuccessful = true, StatusCode = StatusCode.Ok, ResponseType = ResponseType.Ok };
        }
        public static ActionResponse<T> ToPaged(IList<T> items, int page, int pageSize)
        {
            List<T> pagedItems = items.ToList();
            if (pagedItems.Any())
            {
                pagedItems = items.Skip((page - 1) * pageSize).Take(pageSize).ToList();
            }
            return new ActionResponse<T> { Data = default, Items = pagedItems, Page = page, PageSize = pageSize, TotalCount = items.Count, IsSuccessful = true, StatusCode = StatusCode.Ok, ResponseType = ResponseType.Ok };
        }
    }
}
