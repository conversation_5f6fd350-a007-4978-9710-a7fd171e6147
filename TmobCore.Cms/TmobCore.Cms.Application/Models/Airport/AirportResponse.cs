using TmobCore.Cms.Application.Models.City;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Country;
using TmobCore.Cms.Application.Models.Image;

namespace TmobCore.Cms.Application.Models.Airport
{
    public class AirportResponse
    {
        public Guid Id { get; set; }
        public Guid GroupId { get; set; }
        public Guid LanguageId { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public int Order { get; set; }

        public string Slug { get; set; }
        public ImageResponse File { get; set; }

        public CityResponse City { get; set; }
        public CountryResponse Country { get; set; }

        /// <summary>
        /// Status code indicating the operation result for this airport
        /// </summary>
        public int StatusCode { get; set; }

        /// <summary>
        /// List of alternative airports related to this airport
        /// </summary>
        public List<AlternativeAirportResponse> AlternativeAirports { get; set; } = new();
    }

    /// <summary>
    /// Simplified airport response for alternative airports to avoid circular references
    /// </summary>
    public class AlternativeAirportResponse
    {
        public Guid Id { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string Slug { get; set; }
        public BaseStatus Status { get; set; }
        public CityResponse City { get; set; }
        public CountryResponse Country { get; set; }
    }
}
